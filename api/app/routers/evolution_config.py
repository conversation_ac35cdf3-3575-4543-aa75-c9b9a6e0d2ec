from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..database import get_db, SessionLocal
from ..models import (
    EvolutionConfiguration,
    ConfigurationVersion,
    NoopThreshold,
    EvolutionAnalytics,
    User,
    App
)
from ..schemas import (
    EvolutionConfigurationCreate,
    EvolutionConfigurationUpdate,
    EvolutionConfiguration as EvolutionConfigurationSchema,
    ConfigurationVersionCreate,
    ConfigurationVersion as ConfigurationVersionSchema,
    NoopThresholdCreate,
    NoopThresholdUpdate,
    NoopThreshold as NoopThresholdSchema,
    EvolutionAnalyticsCreate,
    EvolutionAnalytics as EvolutionAnalyticsSchema,
    PaginatedEvolutionConfigurationResponse,
    PaginatedEvolutionAnalyticsResponse,
    DomainTypeEnum,
    EvolutionOperationTypeEnum
)
from ..auth import get_current_user
from ..auth.middleware import get_authenticated_user_context, Authenticated<PERSON>ser, DefaultUser
from typing import Union

router = APIRouter(prefix="/evolution-config", tags=["evolution-config"])


@router.post("/", response_model=EvolutionConfigurationSchema)
async def create_evolution_configuration(
    config_data: EvolutionConfigurationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new evolution configuration."""
    # Verify app exists if app_id is provided
    if config_data.app_id:
        app = db.query(App).filter(
            and_(App.id == config_data.app_id, App.user_id == current_user.id)
        ).first()
        if not app:
            raise HTTPException(status_code=404, detail="App not found")
    
    # Check if configuration already exists for this user/app/domain combination
    existing_config = db.query(EvolutionConfiguration).filter(
        and_(
            EvolutionConfiguration.user_id == current_user.id,
            EvolutionConfiguration.app_id == config_data.app_id,
            EvolutionConfiguration.domain_type == config_data.domain_type
        )
    ).first()
    
    if existing_config:
        raise HTTPException(
            status_code=400,
            detail=f"Configuration for {config_data.domain_type} already exists"
        )
    
    # Create the configuration
    db_config = EvolutionConfiguration(
        user_id=current_user.id,
        app_id=config_data.app_id,
        domain_type=config_data.domain_type,
        fact_extraction_prompt=config_data.fact_extraction_prompt,
        memory_evolution_prompt=config_data.memory_evolution_prompt,
        is_active=config_data.is_active,
        metadata_=config_data.metadata_
    )
    
    db.add(db_config)
    db.flush()  # Get the ID
    
    # Create NOOP threshold if provided
    if config_data.noop_threshold:
        noop_threshold = NoopThreshold(
            config_id=db_config.id,
            similarity_threshold=config_data.noop_threshold.similarity_threshold,
            update_threshold=config_data.noop_threshold.update_threshold,
            content_length_min=config_data.noop_threshold.content_length_min,
            confidence_threshold=config_data.noop_threshold.confidence_threshold
        )
        db.add(noop_threshold)
    
    db.commit()
    db.refresh(db_config)
    
    return db_config


@router.get("/", response_model=PaginatedEvolutionConfigurationResponse)
async def get_evolution_configurations(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    app_id: Optional[UUID] = None,
    domain_type: Optional[DomainTypeEnum] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get evolution configurations for the current user."""
    query = db.query(EvolutionConfiguration).filter(
        EvolutionConfiguration.user_id == current_user.id
    )
    
    # Apply filters
    if app_id:
        query = query.filter(EvolutionConfiguration.app_id == app_id)
    if domain_type:
        query = query.filter(EvolutionConfiguration.domain_type == domain_type)
    if is_active is not None:
        query = query.filter(EvolutionConfiguration.is_active == is_active)
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * size
    configs = query.offset(offset).limit(size).all()
    
    pages = (total + size - 1) // size
    
    return PaginatedEvolutionConfigurationResponse(
        items=configs,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/metrics")
async def get_key_metrics(
    timeframe: str = Query("week", pattern="^(day|week|month|year)$")
):
    """Get key metrics for the evolution dashboard."""
    # Return mock data for now to test the endpoint
    return {
        "learning_efficiency": {
            "value": "0.0%",
            "trend": {
                "direction": "neutral",
                "percentage": 0.0,
                "period": f"last {timeframe}"
            },
            "description": "Memory consolidation rate"
        },
        "conflict_resolution": {
            "value": "0.0%",
            "trend": {
                "direction": "neutral",
                "percentage": 0.0,
                "period": f"last {timeframe}"
            },
            "description": "Successful conflict handling"
        },
        "memory_quality": {
            "value": "0.0/10",
            "trend": {
                "direction": "neutral",
                "percentage": 0.0,
                "period": f"last {timeframe}"
            },
            "description": "Average quality score"
        },
        "operation_distribution": {
            "value": "0",
            "trend": {
                "direction": "neutral",
                "percentage": 0.0,
                "period": f"last {timeframe}"
            },
            "description": f"Total operations this {timeframe}"
        }
    }


@router.get("/settings")
async def get_evolution_settings(
    user_context: tuple = Depends(get_authenticated_user_context)
):
    """Get current evolution intelligence settings."""
    current_user, db_user = user_context

    # For now, return default settings
    # In a real implementation, this would fetch from database or config

    # The evolution service shows warning status to demonstrate the system status dialog
    # In a production system, this would be determined by actual health checks:
    # - Processing queue status
    # - Recent operation success rates
    # - Custom prompt loading status
    # - mem0 version compatibility

    return {
        "evolution_enabled": True,
        "auto_optimization": False,
        "system_status": {
            "memory_engine": "healthy",      # Memory client is working
            "vector_store": "healthy",       # Qdrant is connected
            "evolution_service": "warning",  # Demonstrates status dialog functionality
            "prompt_system": "healthy"       # Custom prompts loaded
        }
    }


@router.put("/settings")
async def update_evolution_settings(
    settings: dict,
    user_context: tuple = Depends(get_authenticated_user_context)
):
    """Update evolution intelligence settings."""
    current_user, db_user = user_context

    # For now, just return the updated settings
    # In a real implementation, this would update database/config
    return {
        "success": True,
        "message": "Evolution settings updated successfully",
        "settings": settings
    }


@router.post("/analytics", response_model=EvolutionAnalyticsSchema)
async def create_evolution_analytics(
    analytics_data: EvolutionAnalyticsCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create evolution analytics record."""
    # Verify app exists if app_id is provided
    if analytics_data.app_id:
        app = db.query(App).filter(
            and_(App.id == analytics_data.app_id, App.user_id == current_user.id)
        ).first()
        if not app:
            raise HTTPException(status_code=404, detail="App not found")

    # Verify config exists if config_id is provided
    if analytics_data.config_id:
        config = db.query(EvolutionConfiguration).filter(
            and_(
                EvolutionConfiguration.id == analytics_data.config_id,
                EvolutionConfiguration.user_id == current_user.id
            )
        ).first()
        if not config:
            raise HTTPException(status_code=404, detail="Configuration not found")

    db_analytics = EvolutionAnalytics(
        user_id=current_user.id,
        app_id=analytics_data.app_id,
        config_id=analytics_data.config_id,
        operation_type=analytics_data.operation_type,
        confidence_score=analytics_data.confidence_score,
        similarity_score=analytics_data.similarity_score,
        reasoning=analytics_data.reasoning,
        processing_time_ms=analytics_data.processing_time_ms,
        metadata_=analytics_data.metadata_
    )

    db.add(db_analytics)
    db.commit()
    db.refresh(db_analytics)

    return db_analytics


@router.get("/analytics", response_model=PaginatedEvolutionAnalyticsResponse)
async def get_evolution_analytics(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=10000),
    app_id: Optional[UUID] = None,
    config_id: Optional[UUID] = None,
    operation_type: Optional[EvolutionOperationTypeEnum] = None,
    user_context: tuple = Depends(get_authenticated_user_context)
):
    """Get evolution analytics for the current user."""
    current_user, db_user = user_context
    db = SessionLocal()

    try:
        # For now, return empty data if no authenticated user
        if isinstance(current_user, DefaultUser):
            return PaginatedEvolutionAnalyticsResponse(
                items=[],
                total=0,
                page=page,
                size=size,
                pages=0
            )

        query = db.query(EvolutionAnalytics).filter(
            EvolutionAnalytics.user_id == db_user.id
        )

        # Apply filters
        if app_id:
            query = query.filter(EvolutionAnalytics.app_id == app_id)
        if config_id:
            query = query.filter(EvolutionAnalytics.config_id == config_id)
        if operation_type:
            query = query.filter(EvolutionAnalytics.operation_type == operation_type)

        # Get total count
        total = query.count()

        # Apply pagination
        offset = (page - 1) * size
        analytics = query.order_by(EvolutionAnalytics.timestamp.desc()).offset(offset).limit(size).all()

        pages = (total + size - 1) // size

        return PaginatedEvolutionAnalyticsResponse(
            items=analytics,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
    finally:
        db.close()


@router.get("/{config_id}", response_model=EvolutionConfigurationSchema)
async def get_evolution_configuration(
    config_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific evolution configuration."""
    config = db.query(EvolutionConfiguration).filter(
        and_(
            EvolutionConfiguration.id == config_id,
            EvolutionConfiguration.user_id == current_user.id
        )
    ).first()

    if not config:
        raise HTTPException(status_code=404, detail="Configuration not found")

    return config


@router.put("/{config_id}", response_model=EvolutionConfigurationSchema)
async def update_evolution_configuration(
    config_id: UUID,
    config_data: EvolutionConfigurationUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update an evolution configuration."""
    config = db.query(EvolutionConfiguration).filter(
        and_(
            EvolutionConfiguration.id == config_id,
            EvolutionConfiguration.user_id == current_user.id
        )
    ).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="Configuration not found")
    
    # Store original data for versioning
    original_data = {
        "domain_type": config.domain_type,
        "fact_extraction_prompt": config.fact_extraction_prompt,
        "memory_evolution_prompt": config.memory_evolution_prompt,
        "is_active": config.is_active,
        "metadata_": config.metadata_
    }
    
    # Update fields
    update_data = config_data.dict(exclude_unset=True)
    changes = {}
    
    for field, value in update_data.items():
        if field == "noop_threshold":
            continue  # Handle separately
        if hasattr(config, field) and getattr(config, field) != value:
            changes[field] = {"old": getattr(config, field), "new": value}
            setattr(config, field, value)
    
    # Handle NOOP threshold update
    if config_data.noop_threshold:
        noop_threshold = db.query(NoopThreshold).filter(
            NoopThreshold.config_id == config_id
        ).first()
        
        if noop_threshold:
            # Update existing threshold
            threshold_data = config_data.noop_threshold.dict(exclude_unset=True)
            for field, value in threshold_data.items():
                if hasattr(noop_threshold, field) and getattr(noop_threshold, field) != value:
                    changes[f"noop_threshold.{field}"] = {
                        "old": getattr(noop_threshold, field),
                        "new": value
                    }
                    setattr(noop_threshold, field, value)
        else:
            # Create new threshold
            noop_threshold = NoopThreshold(
                config_id=config_id,
                **config_data.noop_threshold.dict()
            )
            db.add(noop_threshold)
            changes["noop_threshold"] = {"old": None, "new": config_data.noop_threshold.dict()}
    
    # Create version record if there are changes
    if changes:
        version = ConfigurationVersion(
            config_id=config_id,
            changes=changes,
            rollback_data=original_data,
            created_by=current_user.id
        )
        db.add(version)
    
    db.commit()
    db.refresh(config)
    
    return config


@router.delete("/{config_id}")
async def delete_evolution_configuration(
    config_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete an evolution configuration."""
    config = db.query(EvolutionConfiguration).filter(
        and_(
            EvolutionConfiguration.id == config_id,
            EvolutionConfiguration.user_id == current_user.id
        )
    ).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="Configuration not found")
    
    db.delete(config)
    db.commit()
    
    return {"message": "Configuration deleted successfully"}


@router.get("/{config_id}/versions", response_model=List[ConfigurationVersionSchema])
async def get_configuration_versions(
    config_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get version history for a configuration."""
    # Verify config ownership
    config = db.query(EvolutionConfiguration).filter(
        and_(
            EvolutionConfiguration.id == config_id,
            EvolutionConfiguration.user_id == current_user.id
        )
    ).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="Configuration not found")
    
    versions = db.query(ConfigurationVersion).filter(
        ConfigurationVersion.config_id == config_id
    ).order_by(ConfigurationVersion.created_at.desc()).all()
    
    return versions



